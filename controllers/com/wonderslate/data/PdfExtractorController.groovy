package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.McqExtractLog
import com.wonderslate.log.McqTranslationLog
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.springframework.web.multipart.MultipartFile

import org.springframework.http.HttpStatus

class PdfExtractorController {
    PdfExtractorService pdfExtractorService
    DataProviderService dataProviderService
    def springSecurityService
    def redisService

    @Transactional
    def uploadPdfImages() {
        try {
            boolean isQuizImages = false
            boolean isExtractedImages = false
            def bookId = params.long('bookId')
            def chapterId = params.long('chapterId')
            def resId = params.long('resId')
            def quizImages = params.('quizImages')
            def extractedImages = params.('extractedImages')

            if(quizImages){
                isQuizImages = true;
            }

            if(extractedImages){
                isExtractedImages = true
            }
            // Cast request to MultipartHttpServletRequest
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request
            def imageFiles = multiRequest.getFiles("images")
            def croppedImages = multiRequest.getFiles("cropped_images")

            if (!bookId || imageFiles.isEmpty()) {
                render([status: 400, message: "BookId and image files are required"] as JSON)
                return
            }

            def result = pdfExtractorService.processPdfImages(bookId, chapterId, resId, imageFiles, isQuizImages, isExtractedImages, croppedImages)
            if (result.success) {
                render([status: 200, message: "Images uploaded successfully", data: result.data] as JSON)
            } else {
                render([status: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in uploadPdfImages: ${e.message}", e)
            render([status: 500, message: "Internal server error"] as JSON)
        }
    }

    @Transactional
    def getImageLinks() {
        try {
            def bookId = params.long('bookId')
            def chapterId = params.long('chapterId')
            def resId = params.long('resId')
            def quizImages = params.boolean('quizImages')

            if (!bookId) {
                render([status: 400, message: "BookId is required"] as JSON)
                return
            }

            def result = pdfExtractorService.getImagePaths(bookId, chapterId, resId, quizImages, false)
            render([status: result.success ? 200 : 500, data: result.data, message: result.message] as JSON)

        } catch (Exception e) {
            log.error("Error getting image links: ${e.message}", e)
            render([status: 500, message: e.message] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def taskHandler(){
        try {
            def requestBody = request.JSON
            String apiEndpoint = "extract"
            def result = pdfExtractorService.fetchQuestions(request, apiEndpoint, requestBody)
            render([
                    status: result.success ? 200 : 500,
                    data: result.data,
                    message: result.message
            ] as JSON)
        } catch (Exception e) {
            log.error("Error extracting questions: ${e.message}", e)
            render([status: 500, message: e.message] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqExtractor() {
        if ("running".equals("" + redisService.("mcqExtractor_" + params.chapterId))) {
            return [status: 200, error: true, message: "Extraction is already in progress for this chapter.", resLink: null]
        } else {
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(params.chapterId))
            if (chaptersMst == null) {
                return [status: 200, error: true, message: "Chapter details not found.", resLink: null]
            }
            if(chaptersMst.mcqsExtracted!=null&&"true".equals(chaptersMst.mcqsExtracted)){
                return [status: 200, error: true, message: "MCQs are already extracted for this chapter.", resLink: null]
            }
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes", [sort: "id", order: "asc"])
            if (readingMaterials.size() > 0) {
                redisService.("mcqExtractor_" + params.chapterId) = "running"
                params.put("resId", "" + readingMaterials[0].id)
                return [status     : 200, error: false, message: "",
                 resLink    : readingMaterials[0].resLink, bookTitle: booksMst.title,
                 chapterName: chaptersMst.name, chapterId: chaptersMst.id,
                 resId      : readingMaterials[0].id, resName: readingMaterials[0].resourceName
                ]
            } else {
                return [status: 200, error: true, message: "No PDF found for this chapter", resLink: null]
            }
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def startMcqExtraction(){
        try {
            def requestBody = request.JSON

            requestBody.put("username", springSecurityService.currentUser?.username)

            if (!requestBody?.resId) {
                render([status: "error", message: "Resource ID is required"] as JSON)
                return
            }

            if (!requestBody?.total_questions) {
                render([status: "error", message: "Total questions is required"] as JSON)
                return
            }

            if (!requestBody?.explanation_start_page) {
                render([status: "error", message: "Explanation start page is required"] as JSON)
                return
            }

            def result = pdfExtractorService.startMcqExtraction(request, requestBody)

            if (result.success) {
                render(result.data as JSON)
            } else {
                render([status: "error", message: result.message] as JSON)
            }

        } catch (Exception e) {
            log.error("Error in startMcqExtraction: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def downloadMcqTextFile() {
        try {
            def chapterId = params.chapterId
            def resId = params.resId

            if (!chapterId || !resId) {
                render([status: "error", message: "Chapter ID and Resource ID are required"] as JSON)
                return
            }

            def result = pdfExtractorService.downloadMcqTextFile(request, response, chapterId, resId)

            if (!result.success) {
                render([status: "error", message: result.message] as JSON)
            }

        } catch (Exception e) {
            log.error("Error in downloadMcqTextFile: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqExtractorStatus() {
        try {
            def requestBody = request.JSON

            if (!requestBody?.task_id) {
                render([status: "not_found", message: "Task ID is required"] as JSON)
                return
            }

            def result = pdfExtractorService.getMcqExtractorStatus(request, requestBody.task_id)

            if (result.success) {
                render(result.data as JSON)
            } else {
                render([status: "not_found", message: result.message] as JSON)
            }

        } catch (Exception e) {
            log.error("Error in mcqExtractorStatus: ${e.message}", e)
            render([status: "not_found", message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def getMCQText() {
        try {
            def chapterId = params.chapterId
            def resId = params.resId

            if (!chapterId || !resId) {
                render([status: "error", message: "Chapter ID and Resource ID are required"] as JSON)
                return
            }

            def result = pdfExtractorService.getMcqText(request, chapterId, resId)

            if (result.success) {
                render(result.data as JSON)
            } else {
                render([status: "error", message: result.message] as JSON)
            }

        } catch (Exception e) {
            log.error("Error in getMCQText: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }


    @Secured(['ROLE_PDF_EXTRACTOR'])
    def createSolution(){

    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def createMcqSolution(){
        try {
            def multipartFiles = []

            // Get image order information if provided
            def sampleImageOrder = params.sample_image_order ? new groovy.json.JsonSlurper().parseText(params.sample_image_order) : null
            def questionImageOrder = params.question_image_order ? new groovy.json.JsonSlurper().parseText(params.question_image_order) : null

            // Handle sample images with ordering
            def sampleImageFiles = []
            request.multiFileMap.findAll { key, value ->
                key.startsWith('sample_image_')
            }.each { key, files ->
                files.each { file ->
                    // Extract the index from the key (sample_image_X)
                    def index = key.substring('sample_image_'.length()) as Integer
                    sampleImageFiles << [index: index, name: key, file: file]
                }
            }

            // Sort sample images if order is provided
            if (sampleImageOrder) {
                // Reorder the files based on the provided order
                def orderedSampleFiles = []
                sampleImageOrder.eachWithIndex { orderIndex, newIndex ->
                    def fileEntry = sampleImageFiles.find { it.index == orderIndex }
                    if (fileEntry) {
                        // Update the name to reflect the new order
                        fileEntry.name = "sample_image_${newIndex}"
                        orderedSampleFiles << fileEntry
                    }
                }
                // Add ordered files to multipartFiles
                orderedSampleFiles.each { multipartFiles << [name: it.name, file: it.file] }
            } else {
                // No ordering, just add files as they are
                sampleImageFiles.each { multipartFiles << [name: it.name, file: it.file] }
            }

            // Handle question images with ordering
            def questionImageFiles = []
            request.multiFileMap.findAll { key, value ->
                key.startsWith('question_image_')
            }.each { key, files ->
                files.each { file ->
                    // Extract the index from the key (question_image_X)
                    def index = key.substring('question_image_'.length()) as Integer
                    questionImageFiles << [index: index, name: key, file: file]
                }
            }

            // Sort question images if order is provided
            if (questionImageOrder) {
                // Reorder the files based on the provided order
                def orderedQuestionFiles = []
                questionImageOrder.eachWithIndex { orderIndex, newIndex ->
                    def fileEntry = questionImageFiles.find { it.index == orderIndex }
                    if (fileEntry) {
                        // Update the name to reflect the new order
                        fileEntry.name = "question_image_${newIndex}"
                        orderedQuestionFiles << fileEntry
                    }
                }
                // Add ordered files to multipartFiles
                orderedQuestionFiles.each { multipartFiles << [name: it.name, file: it.file] }
            } else {
                // No ordering, just add files as they are
                questionImageFiles.each { multipartFiles << [name: it.name, file: it.file] }
            }

            // Get text inputs
            def sampleText = params.sample_text
            def questionText = params.question_text

            // Call service to create solution
            def result = pdfExtractorService.createMcqSolution(sampleText, questionText, multipartFiles, request)

            render([status: result.success ? 200 : 500, solution: result.solution, ] as JSON)
        } catch (Exception e) {
            log.error("Error processing solution request", e)
            render([success: false, error: e.message] as JSON)
        }
    }

    @Transactional
    def mcqsExtracted(){
        try {
            redisService.("mcqExtractor_"+params.chapterId)=null
            def chaptersMst = ChaptersMst.findById(new Long(params.chapterId))
            chaptersMst.mcqsExtracted="true"
            chaptersMst.save(failOnError: true, flush: true)
            def json = [response:"success"]
            render json as JSON
        }catch (Exception e) {
            log.error("Error processing solution request", e)
            render([response:"failed",success: false, error: e.message] as JSON)
        }

    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def extractAndValidateMcq() {
        try {
            def requestBody = request.JSON
            def resId = requestBody?.resId ?: params.resId

            if (!resId) {
                render([status: 400, message: "Resource ID is required"] as JSON)
                return
            }

            // Extract additional parameters from request body
            def totalQuestions = requestBody?.total_questions
            def explanationStartPage = requestBody?.explanation_start_page
            def forceReextract = requestBody?.force_reextract ?: false

            def result = pdfExtractorService.extractAndValidateMcq(request, resId, session, totalQuestions, explanationStartPage, forceReextract)

            if (result.success) {
                render([status: 200, data: result.data] as JSON)
            } else {
                render([status: 500, message: result.message] as JSON)
            }
        } catch (Exception e) {
            log.error("Error in extractAndValidateMcq: ${e.message}", e)
            render([status: 500, message: "Internal server error: ${e.message}"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqExtractorPage(){
        // Render the MCQ extractor page
        [title: "MCQ Text Extractor"]
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqTranslator(){
        // Render the MCQ translator page
        [title: "MCQ Translator"]
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def startMcqTranslation() {
        try {
            /*
            External API: /mcq-translator-file

            Sample Request:
            Form Data:
            pdfFile: (binary)
            total_questions: 19
            source_language: Hindi
            destination_language: English
            username: web_user

            Sample Response:
            {
                "status": "started",
                "task_id": "45fbb601-0ffe-49fb-8148-79ca60f7156c",
                "message": "MCQ translation task started successfully",
                "response_time": "0.004s"
            }

            IF APPLICABLE USE translateMcqFile METHOD IN SERVICE FILE
             */
        } catch (Exception e) {
            log.error("Error in mcqTranslatorFile: ${e.message}", e)
            render([status: 'error', message: "Internal server error: ${e.message}"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def downloadTranslatedMcqTextFile(){
        try {
            /*
            External API: /download-translated-mcq/task_id

            This api will download the translated mcq text file and we need to get that and pass respond to the gsp with the same downloadable text file
            If applicable use downloadTranslatedFile method in service file
             */
        }catch (Exception e) {
            log.error("Error in downloadTranslatedMcqTextFile: ${e.message}", e)
            render([status: 'error', message: "Internal server error: ${e.message}"] as JSON)
        }
    }


    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def mcqTranslatorStatus() {
        try {
            /*
            External API: /mcq-translator-file/status

            Sample Request:
            {
                "task_id": "45fbb601-0ffe-49fb-8148-79ca60f7156c"
            }

            Sample Response:
            {
                "status": "IN_PROGRESS",
                "task_id": "45fbb601-0ffe-49fb-8148-79ca60f7156c",
                "message": "Translation in progress...",
                "started_at": "2025-06-13T13:10:23",
                "last_updated": "2025-06-13T13:10:23"
            }
             */
        } catch (Exception e) {
            log.error("Error in mcqTranslatorStatus: ${e.message}", e)
            render([status: "not_found", message: "Internal server error"] as JSON)
        }
    }

    @Transactional @Secured(['ROLE_PDF_EXTRACTOR'])
    def getTranslatedText() {
        try {
            /*
            External API: /get-translated-mcq-content/task_id
            This is a get api
            Sample Response:
            {
                "status": "success",
                "data": "Translated text content here..."
            }
             */
        } catch (Exception e) {
            log.error("Error in getTranslatedText: ${e.message}", e)
            render([status: "error", message: "Internal server error"] as JSON)
        }
    }

}
