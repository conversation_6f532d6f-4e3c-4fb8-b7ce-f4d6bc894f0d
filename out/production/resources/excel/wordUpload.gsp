<g:render template="/${session['entryController']}/navheader_new"></g:render>

<form action="upload" method="POST" enctype="multipart/form-data" id="wordForm">
    <div class="custom_container_new">
        <h4 class="text-center my-4">MCQs upload manager</h4>
        <div class='row' >
            <div class='col-md-9 main' style=" margin: 20px auto; float: none; padding: 15px;">
                <div id="content-books">
                    <div class="row">
                        <div class="form-group" style="margin: 10px">
                            <label for="quizName">Enter Quiz name:</label>
                            <input type="text" id="quizName" name="quizName" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group" style="margin: 10px">
                            <label for="quizName">Starting question number:</label>
                            <input type="text" id="startQuestionNo" name="startQuestionNo" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group" style="margin: 10px">
                            <label for="quizName">Ending question number:</label>
                            <input type="text" id="endQuestionNo" name="endQuestionNo" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group" style="margin: 10px">
                            <label for="file">Choose Word File:</label>
                            <input type="file" id="file" name="file" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group"  style="margin: 10px">
                            <button type="button" id="download-btn" class="btn btn-primary btn-lg" style="border-width: 0px;" onclick="handleFileUpload()">Upload</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="chapterId" value="${params.chapterId}">
    <input type="hidden" name="bookId" value="${params.bookId}">
</form>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
        <div id="loadingText" style="color: #ff2a21">Now uploading ....</div>
    </div>

</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    function  handleFileUpload(){
        if(document.getElementById("quizName").value==""){
            document.getElementById("quizName").focus();
            alert("Please enter quiz name");
        }
        else if(document.getElementById("startQuestionNo").value==""){
            document.getElementById("startQuestionNo").focus();
            alert("Please enter starting question number");
        }
        else if(document.getElementById("endQuestionNo").value==""){
            document.getElementById("endQuestionNo").focus();
            alert("Please enter ending question number");
        }
        else{
            $('.loading-icon').removeClass('hidden');
            document.getElementById("wordForm").submit();
        }
    }

    document.getElementById("quizName").focus();
</script>
