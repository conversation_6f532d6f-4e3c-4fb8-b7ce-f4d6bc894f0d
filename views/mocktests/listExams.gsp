<!DOCTYPE html>
<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session["entryController"]}/navheader_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.10.0/js/bootstrap-datepicker.min.js"  crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<asset:stylesheet href="mocktests/mocktests.css" async="true" media="all"/>
<%if("true".equals(session["prepjoySite"])){%>
<style>
body{
    background: #04001D !important;
}
</style>
<%}%>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<main>
    <div class="ws_container">
        <section style="margin-bottom: 2rem;">
            <div class="mockBanner">
                <div class="mockBannerWrap">
                    <p class="testBannerTitle">Best Mock Tests for ${params.examGroup.toUpperCase().replace('-',' ')}</p>
                    <p class="testBannerSub">Get Access to Free ${params.examGroup.toUpperCase().replace('-',' ')} Mock Tests</p>
                </div>
            </div>
        </section>
        <section class="mockTest">
            <h1 class="mockTest__title">Best Mock Tests for ${params.examGroup.toUpperCase().replace('-',' ')}</h1>
            <div class="mockTest__examCards" id="examGroupCards-listExam">
            </div>
        </section>
        <section class="mockTestList">
            <ol class="mockTestList__items" id="mockTestListItems">
            </ol>
        </section>
        <%if(testsList!=null){%>
        <p id="examDescription"></p><br><br>
        <%}%>




    </div>
</main>
<g:render template="/${session["entryController"]}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

<script>
    var exams = JSON.parse("${testsList}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));
    var cardsSrc="";
    var examsList = "";
    var prepjoySite = "${session['prepjoySite']}";
    let level=exams[0].level ? exams[0].level : null;
    let syllabus=exams[0].syllabus ? exams[0].syllabus : null;
    let grade=null;
    let subject=null;
    let pageNo = 0;
    let publisherId=null;
    let subscriptionBooks = false;
    let examCardHTMLListExam = "";
    let testListHTML="";
    const examGroupCardsListExam = document.getElementById('examGroupCards-listExam');
    const mockTestListItems = document.getElementById('mockTestListItems');
    console.log(exams)
    exams.forEach(exam=>{
        examCardHTMLListExam += "<div class='mockTest__examCards-card examCard examCardListExam' data-examName='"+exam.testName+"' data-examId='"+exam.id+"'>"+
                            "<div class='examCard__examName'>"+
                                "<p>"+exam.testName+"</p>"+
                            "</div>"+
                            "<i class='fa-solid fa-chevron-right examCard__arrow'></i>"+
                        "</div>";
    })
    exams.forEach(item=>{
        testListHTML += "<li class='mockTestList__items-item mockTestListItem'>"+
                            "<div"+
                                    "<h2 class='mockTestListItem__title'><a href='/"+item.testName.toLowerCase().replaceAll(' ','-').replaceAll('--','-').replaceAll('--','-')+"/mocktests/all/"+item.id+"' target='_blank'>"+item.testName+"</a></h2>"+
                                    "<p class='mockTestListItem__description'>"+item.testDetails.replaceAll("\\n","").replaceAll("\\t","")+"</p>"+
                            "</div>"+
                        "</li>";
    })
    examGroupCardsListExam.innerHTML = examCardHTMLListExam;
    mockTestListItems.innerHTML = testListHTML;

    const examCardListExam = document.querySelectorAll('.examCardListExam');
    examCardListExam.forEach(item=>{
        item.addEventListener('click',()=>{
            var link = "/"+item.getAttribute('data-examName').toLowerCase().replaceAll(' ','-')+"/mocktests/all/"+item.getAttribute('data-examId');
            setTimeout(()=>{
                window.open(link, '_blank');
            })
        })
    })
</script>


