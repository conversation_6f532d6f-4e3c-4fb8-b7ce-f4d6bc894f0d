<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>

<style>/* Container and form styling */
.test-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.test-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 600px;
}

.test-card h1 {
    text-align: center;
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.test-description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-size: 16px;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.login-form label {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    font-size: 14px;
}

.login-form input[type="text"],
.login-form input[type="number"] {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.login-form input[type="text"]:focus,
.login-form input[type="number"]:focus {
    outline: none;
    border-color: #007bff;
}

.login-form button {
    background-color: #007bff;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

.login-form button:hover {
    background-color: #0056b3;
}

.login-form button:active {
    background-color: #004085;
}

/* Loader Styles */
.lt_loader-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.lt_loader {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loader-text {
    flex: 1;
    max-width: 400px;
}

.lt_loader-title{
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.lt_loader-subtitle{
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 12px;
    font-weight: bold;
    color: #4CAF50;
    min-width: 35px;
}

.translation-results {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.metadata {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.translation-text {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    max-height: 500px;
    overflow-y: auto;
}

.buttons-container {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.view-button, .download-button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.view-button:hover, .download-button:hover {
    background-color: #45a049;
}

.view-button:disabled, .download-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.view-button:disabled:hover, .download-button:disabled:hover {
    background-color: #cccccc;
}

.download-button {
    background-color: #2196F3;
}

.download-button:hover {
    background-color: #1976D2;
}

.link-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.link-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.link-input {
    width: calc(100% - 100px);
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-right: 10px;
    font-family: monospace;
    font-size: 14px;
}

.copy-button {
    background-color: #FF9800;
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.copy-button:hover {
    background-color: #F57C00;
}
select[name='sourceLanguage'],
select[name='destinationLanguage']{
    padding: 0.6rem !important;
    background: #f4f4f4;
    border-radius: 6px !important;
    border: 1px solid #ccc !important;
    font-size: 1rem !important;
    margin-bottom: 1.2rem !important;
}

/* Progress Log Styles */
#logContainer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
    margin-top: 20px;
}

#logContainer h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

#logContainer h3::before {
    content: "📋";
    font-size: 20px;
}

#timer {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white !important;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 2px 8px rgba(0,123,255,0.3); }
    50% { box-shadow: 0 2px 15px rgba(0,123,255,0.5); }
    100% { box-shadow: 0 2px 8px rgba(0,123,255,0.3); }
}

.progress-log {
    background: #ffffff;
    border-radius: 8px;
    padding: 15px;
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.progress-log::-webkit-scrollbar {
    width: 8px;
}

.progress-log::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.progress-log::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 4px;
}

.progress-log::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
}

.log-entry {
    padding: 8px 12px;
    margin-bottom: 6px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.4;
    position: relative;
    animation: slideIn 0.3s ease-out;
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.log-entry:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

/* Different styles for different types of log entries */
.log-entry:contains("Error") {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545;
}

.log-entry:contains("completed") {
    background-color: #d4edda;
    color: #155724;
    border-left-color: #28a745;
}

.log-entry:contains("Starting") {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8;
}

.log-entry:contains("Status:") {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: #ffc107;
}

.log-entry:contains("Task started") {
    background-color: #e2e3e5;
    color: #383d41;
    border-left-color: #6c757d;
    font-weight: 500;
}

.log-entry::before {
    content: "•";
    color: #6c757d;
    margin-right: 8px;
    font-weight: bold;
}

/* Add timestamp to log entries */
.log-entry::after {
    content: attr(data-time);
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 11px;
    color: #6c757d;
    opacity: 0.7;
}

/* Empty state for progress log */
.progress-log:empty::before {
    content: "No logs yet...";
    display: block;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}
.pyqs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}
</style>
<%
    String backNav
    if ('books'.equals(session['entryController'])){
        backNav = "/${session['entryController']}/home"
    }else if('privatelabel'.equals(session['entryController'])){
        backNav = "/${session['entryController']}/admin"
    }
%>
<div class="test-container">
    <div class="pyqs-header mt-5">
        <a href="${backNav}" class="back-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"/>
            </svg>
            Back to Admin
        </a>
    </div>
    <div class="test-card">
        <h1>MCQ Translator</h1>
        <p class="test-description">Translate MCQs from one language to another</p>

        <form id="translatorForm" class="login-form" enctype="multipart/form-data">
            <label for="pdfFile">Upload PDF File</label>
            <input type="file" id="pdfFile" name="pdfFile" accept=".pdf" required>

            <label for="totalQuestions">Total Questions</label>
            <input type="number" id="totalQuestions" name="totalQuestions" min="1" value="10" required>

            <label for="sourceLanguage">Source Language</label>
            <select id="sourceLanguage" name="sourceLanguage" required>
                <option value="English">English</option>
                <option value="Hindi">Hindi</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <label for="destinationLanguage">Destination Language</label>
            <select id="destinationLanguage" name="destinationLanguage" required>
                <option value="Hindi">Hindi</option>
                <option value="English">English</option>
                <option value="Tamil">Tamil</option>
                <option value="Assamese">Assamese</option>
                <option value="Telugu">Telugu</option>
                <option value="Kannada">Kannada</option>
                <option value="Malayalam">Malayalam</option>
                <option value="Marathi">Marathi</option>
                <option value="Bengali">Bengali</option>
                <option value="Gujarati">Gujarati</option>
                <option value="Urdu">Urdu</option>
            </select>

            <button type="submit">Translate MCQ</button>
        </form>
        <div id="loaderContainer" style="display: none; margin-top: 20px;">
            <div class="lt_loader-wrapper">
                <div class="lt_loader"></div>
                <div class="lt_loader-text">
                    <div id="loaderTitle" class="lt_loader-title">Processing Translation...</div>
                    <div id="loaderSubtitle" class="lt_loader-subtitle">Please wait while we process your PDF file</div>
                    <div class="progress-bar-container" style="display: none;">
                        <div class="progress-bar">
                            <div id="progressBarFill" class="progress-bar-fill"></div>
                        </div>
                        <div id="progressText" class="progress-text">0%</div>
                    </div>
                </div>
            </div>
        </div>

        <div id="logContainer" style="display: none; margin-top: 20px;">
            <h3>Progress Log: <span id="timer" style="color: #007bff; font-weight: bold;">00:00</span></h3>
            <div id="progressLog" class="progress-log"></div>
        </div>

        <div id="resultContainer" style="display: none; margin-top: 20px;">
            <h3>Translation Results:</h3>
            <div id="translationResults" class="translation-results"></div>
        </div>
    </div>
</div>

<script src="/assets/bookGPTScripts/mcqTranslator.js"></script>